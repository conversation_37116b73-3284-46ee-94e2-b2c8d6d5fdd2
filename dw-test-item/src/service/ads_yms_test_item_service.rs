use crate::config::DwTestItemConfig;
use common::dto::ads::key::test_item_bin_key::TestItemBinKey;
use common::dto::ads::key::test_item_program_key::TestItemProgramKey;
use common::dto::ads::key::test_item_site_bin_key::TestItemSiteBinKey;
use common::dto::ads::key::test_item_site_key::TestItemSiteKey;
use common::dto::ads::value::test_item_bin::TestItemBin;
use common::dto::ads::value::test_item_detail::TestItemDetail;
use common::dto::ads::value::test_item_program::TestItemProgram;
use common::dto::ads::value::test_item_site::TestItemSite;
use common::dto::ads::value::test_item_site_bin::TestItemSiteBin;
use common::dto::ods::product_config::OdsProductConfig;
use std::collections::HashMap;
use std::error::Error;
use std::sync::Arc;
use common::model::constant::P;

/// Grouped test item details for different aggregation levels
/// Contains both total and pass-only groups for performance optimization
#[derive(Debug)]
struct GroupedTestItemDetails {
    /// Groups for TestItemProgram aggregation (by program key) - all records
    program_groups_total: HashMap<TestItemProgramKey, Vec<Arc<TestItemDetail>>>,
    /// Groups for TestItemProgram aggregation (by program key) - pass-only records
    program_groups_pass: HashMap<TestItemProgramKey, Vec<Arc<TestItemDetail>>>,

    /// Groups for TestItemSite aggregation (by site key) - all records
    site_groups_total: HashMap<TestItemSiteKey, Vec<Arc<TestItemDetail>>>,
    /// Groups for TestItemSite aggregation (by site key) - pass-only records
    site_groups_pass: HashMap<TestItemSiteKey, Vec<Arc<TestItemDetail>>>,

    /// Groups for TestItemBin aggregation (by bin key) - all records
    bin_groups_total: HashMap<TestItemBinKey, Vec<Arc<TestItemDetail>>>,
    /// Groups for TestItemBin aggregation (by bin key) - pass-only records
    bin_groups_pass: HashMap<TestItemBinKey, Vec<Arc<TestItemDetail>>>,

    /// Groups for TestItemSiteBin aggregation (by site-bin key) - all records
    site_bin_groups_total: HashMap<TestItemSiteBinKey, Vec<Arc<TestItemDetail>>>,
    /// Groups for TestItemSiteBin aggregation (by site-bin key) - pass-only records
    site_bin_groups_pass: HashMap<TestItemSiteBinKey, Vec<Arc<TestItemDetail>>>,
}

/// ADS YMS Test Item Service handles transformation from DWD to ADS layer
/// This service processes test item data for YMS (Yield Management System) analytics
///
/// Corresponds to the ADS layer processing in the original Scala implementation
#[derive(Debug, Clone)]
pub struct AdsYmsTestItemService {
    /// Configuration properties for the service
    properties: DwTestItemConfig,
    /// Test area (CP or FT)
    test_area: String,
}

impl AdsYmsTestItemService {

    pub fn new(properties: DwTestItemConfig, test_area: String) -> Self {
        Self { properties, test_area }
    }

    /// Calculate all ADS test item aggregations
    ///
    /// This method replicates the Scala calculateAll functionality:
    /// def calculateAll(spark: SparkSession, testItemDetail: Dataset[TestItemDetail],
    ///                  productList: Broadcast[List[OdsProductConfig]]):
    ///                  (Dataset[TestItemProgram], Dataset[TestItemSite], Dataset[TestItemBin], Dataset[TestItemSiteBin])
    ///
    /// Each aggregation type generates TWO records:
    /// - Total: All records aggregated (parameter 0)
    /// - Pass-only: Only passed records aggregated (parameter 1, TEST_RESULT = 1)
    ///
    /// # Arguments
    /// * `test_item_details` - Vector of TestItemDetail records to process
    /// * `product_list` - List of product configurations for product info lookup
    ///
    /// # Returns
    /// A tuple containing (TestItemProgram, TestItemSite, TestItemBin, TestItemSiteBin) vectors
    /// Each vector contains both total and pass-only aggregations
    pub fn calculate_all(
        &self,
        test_item_details: &Vec<Arc<TestItemDetail>>,
        product_list: &[OdsProductConfig],
    ) -> Result<(Vec<TestItemProgram>, Vec<TestItemSite>, Vec<TestItemBin>, Vec<TestItemSiteBin>), Box<dyn Error>> {

        // Group test item details by different aggregation keys
        let grouped_data = self.group_test_item_details(test_item_details)?;

        // Process each group to generate aggregated results
        let mut test_item_programs = Vec::new();
        let mut test_item_sites = Vec::new();
        let mut test_item_bins = Vec::new();
        let mut test_item_site_bins = Vec::new();

        // Process program-level aggregations
        // Total aggregations (all records, parameter 0)
        for (_, items) in &grouped_data.program_groups_total {
            let total_program = TestItemProgram::from_test_items(items, 0, product_list);
            test_item_programs.push(total_program);
        }
        // Pass-only aggregations (pass records only, parameter 1)
        for (_, items) in &grouped_data.program_groups_pass {
            let pass_only_program = TestItemProgram::from_test_items(items, 1, product_list);
            test_item_programs.push(pass_only_program);
        }

        // Process site-level aggregations
        // Total aggregations (all records, parameter 0)
        for (_, items) in &grouped_data.site_groups_total {
            let total_site = TestItemSite::from_test_items(items, 0, product_list);
            test_item_sites.push(total_site);
        }
        // Pass-only aggregations (pass records only, parameter 1)
        for (_, items) in &grouped_data.site_groups_pass {
            let pass_only_site = TestItemSite::from_test_items(items, 1, product_list);
            test_item_sites.push(pass_only_site);
        }

        // Process bin-level aggregations
        // Total aggregations (all records, parameter 0)
        for (_, items) in &grouped_data.bin_groups_total {
            let total_bin = TestItemBin::from_test_items(items, 0, product_list);
            test_item_bins.push(total_bin);
        }
        // Pass-only aggregations (pass records only, parameter 1)
        for (_, items) in &grouped_data.bin_groups_pass {
            let pass_only_bin = TestItemBin::from_test_items(items, 1, product_list);
            test_item_bins.push(pass_only_bin);
        }

        // Process site-bin-level aggregations
        // Total aggregations (all records, parameter 0)
        for (_, items) in &grouped_data.site_bin_groups_total {
            let total_site_bin = TestItemSiteBin::from_test_items(items, 0, product_list);
            test_item_site_bins.push(total_site_bin);
        }
        // Pass-only aggregations (pass records only, parameter 1)
        for (_, items) in &grouped_data.site_bin_groups_pass {
            let pass_only_site_bin = TestItemSiteBin::from_test_items(items, 1, product_list);
            test_item_site_bins.push(pass_only_site_bin);
        }

        Ok((test_item_programs, test_item_sites, test_item_bins, test_item_site_bins))
    }

    /// Group test item details by different aggregation keys
    ///
    /// This method groups the test item details according to the keys used by each aggregation type.
    /// For performance optimization, it creates both total and pass-only groups in a single pass:
    /// - Program: Groups by program-related fields (total + pass-only)
    /// - Site: Groups by site-related fields (total + pass-only)
    /// - Bin: Groups by bin-related fields (total + pass-only)
    /// - SiteBin: Groups by both site and bin related fields (total + pass-only)
    fn group_test_item_details(
        &self,
        test_item_details: &Vec<Arc<TestItemDetail>>,
    ) -> Result<GroupedTestItemDetails, Box<dyn Error>> {

        // Initialize all group maps
        let mut program_groups_total: HashMap<TestItemProgramKey, Vec<Arc<TestItemDetail>>> = HashMap::new();
        let mut program_groups_pass: HashMap<TestItemProgramKey, Vec<Arc<TestItemDetail>>> = HashMap::new();
        let mut site_groups_total: HashMap<TestItemSiteKey, Vec<Arc<TestItemDetail>>> = HashMap::new();
        let mut site_groups_pass: HashMap<TestItemSiteKey, Vec<Arc<TestItemDetail>>> = HashMap::new();
        let mut bin_groups_total: HashMap<TestItemBinKey, Vec<Arc<TestItemDetail>>> = HashMap::new();
        let mut bin_groups_pass: HashMap<TestItemBinKey, Vec<Arc<TestItemDetail>>> = HashMap::new();
        let mut site_bin_groups_total: HashMap<TestItemSiteBinKey, Vec<Arc<TestItemDetail>>> = HashMap::new();
        let mut site_bin_groups_pass: HashMap<TestItemSiteBinKey, Vec<Arc<TestItemDetail>>> = HashMap::new();

        for detail in test_item_details {
            // Create keys for each aggregation level
            let program_key = TestItemProgramKey::from_test_item_detail(&detail);
            let site_key = TestItemSiteKey::from_test_item_detail(&detail);
            let bin_key = TestItemBinKey::from_test_item_detail(&detail);
            let site_bin_key = TestItemSiteBinKey::from_test_item_detail(&detail);

            // Check if this is a pass record
            let is_pass = detail.HBIN_PF.as_ref() == P;

            // Add to pass-only groups if this is a pass record
            if is_pass {
                program_groups_pass.entry(program_key.clone()).or_insert_with(Vec::new).push(detail.clone());
                site_groups_pass.entry(site_key.clone()).or_insert_with(Vec::new).push(detail.clone());
                bin_groups_pass.entry(bin_key.clone()).or_insert_with(Vec::new).push(detail.clone());
                site_bin_groups_pass.entry(site_bin_key.clone()).or_insert_with(Vec::new).push(detail.clone());
            }

            // Add to total groups (all records)
            program_groups_total.entry(program_key).or_insert_with(Vec::new).push(detail.clone());
            site_groups_total.entry(site_key).or_insert_with(Vec::new).push(detail.clone());
            bin_groups_total.entry(bin_key).or_insert_with(Vec::new).push(detail.clone());
            site_bin_groups_total.entry(site_bin_key).or_insert_with(Vec::new).push(detail.clone());
        }

        Ok(GroupedTestItemDetails {
            program_groups_total,
            program_groups_pass,
            site_groups_total,
            site_groups_pass,
            bin_groups_total,
            bin_groups_pass,
            site_bin_groups_total,
            site_bin_groups_pass,
        })
    }

}
